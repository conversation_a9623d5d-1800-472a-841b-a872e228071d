/**
 * MoodifyMe - Main JavaScript
 * Contains common functionality used throughout the application
 */

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Handle form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Handle mood input option selection
    const inputOptions = document.querySelectorAll('.input-option');
    const inputForms = document.querySelectorAll('.mood-input-form');

    if (inputOptions.length > 0 && inputForms.length > 0) {
        inputOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Remove active class from all options
                inputOptions.forEach(opt => opt.classList.remove('active'));

                // Add active class to clicked option
                this.classList.add('active');

                // Hide all input forms
                inputForms.forEach(form => form.style.display = 'none');

                // Show the corresponding input form
                const formId = this.id.replace('-option', '-form');
                document.getElementById(formId).style.display = 'block';
            });
        });
    }

    // Handle dark mode toggle
    const darkModeToggle = document.getElementById('darkModeToggle');
    if (darkModeToggle) {
        darkModeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-mode');

            // Save preference to localStorage
            if (document.body.classList.contains('dark-mode')) {
                localStorage.setItem('darkMode', 'enabled');
            } else {
                localStorage.setItem('darkMode', 'disabled');
            }
        });

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'enabled') {
            document.body.classList.add('dark-mode');
            darkModeToggle.checked = true;
        }
    }

    // Handle notification dismissal
    const notifications = document.querySelectorAll('.notification-item');
    if (notifications.length > 0) {
        notifications.forEach(notification => {
            const dismissBtn = notification.querySelector('.dismiss-notification');
            if (dismissBtn) {
                dismissBtn.addEventListener('click', function() {
                    notification.style.opacity = '0';
                    setTimeout(() => {
                        notification.style.display = 'none';
                    }, 300);

                    // You can add AJAX call here to mark notification as read in the database
                });
            }
        });
    }

    // Handle recommendation feedback
    const feedbackButtons = document.querySelectorAll('.recommendation-feedback');
    if (feedbackButtons.length > 0) {
        feedbackButtons.forEach(button => {
            button.addEventListener('click', function() {
                const recommendationId = this.dataset.recommendationId;
                const feedbackType = this.dataset.feedbackType;

                // Send feedback to server via AJAX
                fetch('api/recommendation_feedback.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        recommendation_id: recommendationId,
                        feedback_type: feedbackType
                    }),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update UI to show feedback was received
                        this.classList.add('active');

                        // If this is a like/dislike system, toggle the other button off
                        if (feedbackType === 'like' || feedbackType === 'dislike') {
                            const oppositeType = feedbackType === 'like' ? 'dislike' : 'like';
                            const oppositeButton = document.querySelector(`.recommendation-feedback[data-recommendation-id="${recommendationId}"][data-feedback-type="${oppositeType}"]`);
                            if (oppositeButton) {
                                oppositeButton.classList.remove('active');
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
            });
        });
    }

    // Handle mood analysis form submission (text input)
    const moodTextForm = document.getElementById('mood-text-form');
    if (moodTextForm) {
        moodTextForm.addEventListener('submit', function(event) {
            event.preventDefault();

            const moodText = document.getElementById('mood-text').value;
            if (!moodText.trim()) {
                alert('Please describe your mood before submitting.');
                return;
            }

            // Show loading indicator
            const submitButton = moodTextForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
            submitButton.disabled = true;

            // Send form data to server via AJAX
            const formData = new FormData(moodTextForm);

            fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show detected emotion and ask for target emotion
                    if (typeof window.showEmotionResults === 'function') {
                        window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                    } else {
                        console.error('showEmotionResults function not found');
                        alert('Error: Could not display emotion results. Please try again.');
                    }

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                } else {
                    // Show error message
                    alert('Error analyzing mood: ' + data.message);

                    // Reset button
                    submitButton.innerHTML = originalButtonText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while analyzing your mood. Please try again.');

                // Reset button
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            });
        });
    }

    // Handle voice recording with speech-to-text
    const startRecordingBtn = document.getElementById('start-recording');
    const stopRecordingBtn = document.getElementById('stop-recording');
    const submitVoiceBtn = document.getElementById('submit-voice');
    const recordingStatus = document.getElementById('recording-status');
    const audioDataInput = document.getElementById('audio-data');
    const voiceForm = document.getElementById('voice-form');

    if (startRecordingBtn && stopRecordingBtn && recordingStatus) {
        let recognition;
        let isRecording = false;
        let transcribedText = '';

        // Check if browser supports speech recognition
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
            recordingStatus.textContent = 'Speech recognition not supported in this browser. Please use Chrome, Edge, or Safari.';
            startRecordingBtn.disabled = true;
            return;
        }

        // Initialize speech recognition
        recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US';

        startRecordingBtn.addEventListener('click', function() {
            // Reset previous recording
            transcribedText = '';
            audioDataInput.value = '';
            submitVoiceBtn.style.display = 'none';
            isRecording = true;

            // Start speech recognition
            try {
                recognition.start();
                startRecordingBtn.style.display = 'none';
                stopRecordingBtn.style.display = 'inline-block';
                recordingStatus.innerHTML = '<i class="fas fa-microphone text-danger"></i> Listening... Speak about how you feel.';
            } catch (error) {
                console.error('Error starting speech recognition:', error);
                recordingStatus.innerHTML = `<i class="fas fa-exclamation-circle text-danger"></i> Error starting speech recognition. Please try again.`;
                startRecordingBtn.style.display = 'inline-block';
                stopRecordingBtn.style.display = 'none';
                isRecording = false;
            }
        });

        stopRecordingBtn.addEventListener('click', function() {
            if (isRecording) {
                recognition.stop();
                isRecording = false;
                startRecordingBtn.style.display = 'inline-block';
                stopRecordingBtn.style.display = 'none';
            }
        });

        // Handle speech recognition results
        recognition.onresult = function(event) {
            let interimTranscript = '';
            let finalTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            // Update transcribed text - accumulate all final results
            if (finalTranscript) {
                transcribedText += finalTranscript;
                console.log('Final transcript added:', finalTranscript);
                console.log('Total transcribed text:', transcribedText);
            }

            // Show real-time transcription
            if (finalTranscript || interimTranscript) {
                recordingStatus.innerHTML = `
                    <i class="fas fa-microphone text-danger"></i> Listening...<br>
                    <small class="text-muted">You said: "${transcribedText}<span class="text-info">${interimTranscript}</span>"</small>
                `;
            }
        };

        // Handle speech recognition end
        recognition.onend = function() {
            console.log('Speech recognition ended. Transcribed text:', transcribedText);
            isRecording = false;
            startRecordingBtn.style.display = 'inline-block';
            stopRecordingBtn.style.display = 'none';

            if (transcribedText.trim()) {
                // Store transcribed text for submission
                audioDataInput.value = transcribedText.trim();
                submitVoiceBtn.style.display = 'inline-block';
                console.log('Audio data input value set to:', audioDataInput.value);
                recordingStatus.innerHTML = `
                    <i class="fas fa-check text-success"></i> Recording complete!<br>
                    <small class="text-muted">Transcribed: "${transcribedText.trim()}"</small><br>
                    <small>Click "Analyze My Mood" to continue.</small>
                `;
            } else {
                console.log('No transcribed text available');
                recordingStatus.innerHTML = `
                    <i class="fas fa-exclamation-triangle text-warning"></i> No speech detected.<br>
                    <small>Please try speaking more clearly or check your microphone.</small>
                `;
            }
        };

        // Handle speech recognition errors
        recognition.onerror = function(event) {
            console.error('Speech recognition error:', event.error);
            isRecording = false;
            startRecordingBtn.style.display = 'inline-block';
            stopRecordingBtn.style.display = 'none';

            let errorMessage = 'Speech recognition error. ';
            switch (event.error) {
                case 'no-speech':
                    errorMessage += 'No speech was detected. Please try again.';
                    break;
                case 'audio-capture':
                    errorMessage += 'No microphone was found. Please check your microphone.';
                    break;
                case 'not-allowed':
                    errorMessage += 'Microphone permission denied. Please allow microphone access.';
                    break;
                case 'network':
                    errorMessage += 'Network error occurred. Please check your internet connection.';
                    break;
                default:
                    errorMessage += 'Please try again.';
            }
            recordingStatus.innerHTML = `<i class="fas fa-exclamation-circle text-danger"></i> ${errorMessage}`;
        };

        // Handle voice form submission
        if (voiceForm) {
            voiceForm.addEventListener('submit', function(event) {
                event.preventDefault();

                if (!audioDataInput.value) {
                    alert('Please record your voice first.');
                    return;
                }

                // Debug: Check what we have
                console.log('Audio data input value:', audioDataInput.value);
                console.log('Audio data input length:', audioDataInput.value.length);

                // Show loading indicator
                submitVoiceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
                submitVoiceBtn.disabled = true;

                // Send form data to server via AJAX
                const formData = new FormData(voiceForm);

                // Debug: Check form data
                console.log('Form data entries:');
                for (let [key, value] of formData.entries()) {
                    console.log(key + ':', value);
                }

                fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Voice analysis response:', data);

                    if (data.success) {
                        // Show detected emotion and ask for target emotion
                        if (typeof window.showEmotionResults === 'function') {
                            window.showEmotionResults(data.emotion, data.confidence, data.emotion_id, data.needs_clarification, data.clarification_message);
                        } else {
                            console.error('showEmotionResults function not found');
                            alert('Error: Could not display emotion results. Please try again.');
                        }

                        // Reset button
                        submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitVoiceBtn.disabled = false;
                    } else {
                        // Show error message
                        alert('Error analyzing mood: ' + data.message);

                        // Reset button
                        submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitVoiceBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while analyzing your mood. Please try again.');

                    // Reset button
                    submitVoiceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                    submitVoiceBtn.disabled = false;
                });
            });
        }
    }

    // Handle face capture
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const captureButton = document.getElementById('capture-button');
    const submitFaceBtn = document.getElementById('submit-face');
    const imageDataInput = document.getElementById('image-data');
    const facialLoading = document.getElementById('facial-loading');
    const emotionResult = document.getElementById('emotion-result');
    const faceForm = document.getElementById('face-form');

    if (video && canvas && captureButton) {
        // Start video stream when face input is selected
        document.getElementById('face-input-option').addEventListener('click', function() {
            // Reset previous capture
            imageDataInput.value = '';
            submitFaceBtn.style.display = 'none';
            emotionResult.innerHTML = '';

            // Start video stream
            navigator.mediaDevices.getUserMedia({ video: true })
                .then(stream => {
                    video.srcObject = stream;
                })
                .catch(error => {
                    console.error('Error accessing camera:', error);
                    alert('Error accessing camera. Please check permissions.');
                });
        });

        captureButton.addEventListener('click', function() {
            if (video.srcObject) {
                // Draw video frame to canvas
                const context = canvas.getContext('2d');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // Convert to base64 for sending to server
                const imageData = canvas.toDataURL('image/png');
                imageDataInput.value = imageData;

                // Show loading indicator
                facialLoading.style.display = 'block';
                captureButton.disabled = true;

                // In a real implementation, you would analyze the image here
                // For this example, we'll simulate a delay and show a random emotion
                setTimeout(() => {
                    facialLoading.style.display = 'none';
                    captureButton.disabled = false;

                    // Show a random emotion result
                    const emotions = Object.keys(EMOTION_CATEGORIES || {
                        'happy': 'Happy',
                        'sad': 'Sad',
                        'angry': 'Angry',
                        'anxious': 'Anxious',
                        'calm': 'Calm'
                    });
                    const randomEmotion = emotions[Math.floor(Math.random() * emotions.length)];

                    emotionResult.innerHTML = `
                        <div class="alert alert-info">
                            <h5>Detected Emotion: <strong>${randomEmotion.charAt(0).toUpperCase() + randomEmotion.slice(1)}</strong></h5>
                            <p>Confidence: 85%</p>
                        </div>
                    `;

                    // Show submit button
                    submitFaceBtn.style.display = 'inline-block';
                }, 2000);

                // Stop video stream
                const stream = video.srcObject;
                const tracks = stream.getTracks();
                tracks.forEach(track => track.stop());
                video.srcObject = null;
            }
        });

        // Handle face form submission
        if (faceForm) {
            faceForm.addEventListener('submit', function(event) {
                event.preventDefault();

                if (!imageDataInput.value) {
                    alert('Please capture your expression first.');
                    return;
                }

                // Show loading indicator
                submitFaceBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Analyzing...';
                submitFaceBtn.disabled = true;

                // Send form data to server via AJAX
                const formData = new FormData(faceForm);

                fetch(window.location.pathname.includes('/pages/') ? '../api/emotion_analysis.php' : 'api/emotion_analysis.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show detected emotion and ask for target emotion
                        if (typeof window.showEmotionResults === 'function') {
                            window.showEmotionResults(data.emotion, data.confidence, data.emotion_id);
                        } else {
                            console.error('showEmotionResults function not found');
                            alert('Error: Could not display emotion results. Please try again.');
                        }

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    } else {
                        // Show error message
                        alert('Error analyzing mood: ' + data.message);

                        // Reset button
                        submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                        submitFaceBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while analyzing your mood. Please try again.');

                    // Reset button
                    submitFaceBtn.innerHTML = '<i class="fas fa-brain me-2"></i> Analyze My Mood';
                    submitFaceBtn.disabled = false;
                });
            });
        }
    }
});
