<?php
/**
 * Simple test for voice input
 */

// Start session
session_start();

// Simulate logged in user
$_SESSION['user_id'] = 1;

echo "Testing voice input processing...\n\n";

// Test 1: Check if we can simulate the POST data
echo "=== Test 1: Simulating POST data ===\n";
$_POST['input_type'] = 'voice';
$_POST['audio_data'] = 'I feel happy today';

echo "POST data set:\n";
echo "input_type: " . $_POST['input_type'] . "\n";
echo "audio_data: " . $_POST['audio_data'] . "\n\n";

// Test 2: Check if the emotion analysis API can process this
echo "=== Test 2: Processing with emotion analysis ===\n";

// Test the API via HTTP request instead
$url = 'http://localhost:8000/api/emotion_analysis.php';
$postData = [
    'input_type' => 'voice',
    'audio_data' => 'I feel happy today'
];

$ch = curl_init($url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "cURL Error: $error\n";
} else {
    echo "HTTP Code: $httpCode\n";
    echo "Response: $response\n";
}

echo "\n=== Test Complete ===\n";
